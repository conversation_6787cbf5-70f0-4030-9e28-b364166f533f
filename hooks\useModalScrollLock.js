import { useEffect } from 'react';

/**
 * Hook personalizado para bloquear o scroll da página quando um modal está aberto
 * @param {boolean} isOpen - Se o modal está aberto ou não
 */
export const useModalScrollLock = (isOpen) => {
  useEffect(() => {
    if (isOpen) {
      // Salvar posição atual do scroll
      const scrollY = window.scrollY;

      // Salvar valores originais
      const originalBodyOverflow = document.body.style.overflow;
      const originalHtmlOverflow = document.documentElement.style.overflow;
      const originalBodyPosition = document.body.style.position;
      const originalBodyTop = document.body.style.top;

      // APLICAR BLOQUEIO ULTRA AGRESSIVO
      // Forçar overflow hidden em múltiplas propriedades
      document.body.style.setProperty('overflow', 'hidden', 'important');
      document.body.style.setProperty('overflow-x', 'hidden', 'important');
      document.body.style.setProperty('overflow-y', 'hidden', 'important');
      document.body.style.setProperty('position', 'fixed', 'important');
      document.body.style.setProperty('top', `-${scrollY}px`, 'important');
      document.body.style.setProperty('width', '100%', 'important');
      document.body.style.setProperty('height', '100vh', 'important');

      document.documentElement.style.setProperty('overflow', 'hidden', 'important');
      document.documentElement.style.setProperty('overflow-x', 'hidden', 'important');
      document.documentElement.style.setProperty('overflow-y', 'hidden', 'important');

      // CSS simples para bloquear scroll da página
      const styleElement = document.createElement('style');
      styleElement.id = 'modal-scroll-lock';
      styleElement.innerHTML = `
        html, body {
          overflow: hidden !important;
          height: 100% !important;
        }

        /* Permitir scroll apenas nos modais */
        .tutorialModal,
        .profileModal,
        .menuContainer,
        .modal,
        .friendsModal,
        .inviteModal,
        .errorModal,
        .avatarModal,
        .referralModal {
          overflow-y: auto !important;
          overflow-x: hidden !important;
        }

        /* Scrollbar customizada para todos os modais */
        .tutorialModal::-webkit-scrollbar,
        .profileModal::-webkit-scrollbar,
        .menuContainer::-webkit-scrollbar,
        .modal::-webkit-scrollbar,
        .friendsModal::-webkit-scrollbar,
        .inviteModal::-webkit-scrollbar,
        .errorModal::-webkit-scrollbar,
        .avatarModal::-webkit-scrollbar,
        .referralModal::-webkit-scrollbar {
          width: 8px;
        }

        .tutorialModal::-webkit-scrollbar-track,
        .profileModal::-webkit-scrollbar-track,
        .menuContainer::-webkit-scrollbar-track,
        .modal::-webkit-scrollbar-track,
        .friendsModal::-webkit-scrollbar-track,
        .inviteModal::-webkit-scrollbar-track,
        .errorModal::-webkit-scrollbar-track,
        .avatarModal::-webkit-scrollbar-track,
        .referralModal::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        .tutorialModal::-webkit-scrollbar-thumb,
        .profileModal::-webkit-scrollbar-thumb,
        .menuContainer::-webkit-scrollbar-thumb,
        .modal::-webkit-scrollbar-thumb,
        .friendsModal::-webkit-scrollbar-thumb,
        .inviteModal::-webkit-scrollbar-thumb,
        .errorModal::-webkit-scrollbar-thumb,
        .avatarModal::-webkit-scrollbar-thumb,
        .referralModal::-webkit-scrollbar-thumb {
          background: rgba(29, 185, 84, 0.5);
          border-radius: 4px;
          transition: background 0.3s ease;
        }

        .tutorialModal::-webkit-scrollbar-thumb:hover,
        .profileModal::-webkit-scrollbar-thumb:hover,
        .menuContainer::-webkit-scrollbar-thumb:hover,
        .modal::-webkit-scrollbar-thumb:hover,
        .friendsModal::-webkit-scrollbar-thumb:hover,
        .inviteModal::-webkit-scrollbar-thumb:hover,
        .errorModal::-webkit-scrollbar-thumb:hover,
        .avatarModal::-webkit-scrollbar-thumb:hover,
        .referralModal::-webkit-scrollbar-thumb:hover {
          background: rgba(29, 185, 84, 0.7);
        }

        /* Firefox scrollbar */
        .tutorialModal,
        .profileModal,
        .menuContainer,
        .modal,
        .friendsModal,
        .inviteModal,
        .errorModal,
        .avatarModal,
        .referralModal {
          scrollbar-width: thin;
          scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
        }
      `;
      document.head.appendChild(styleElement);

      // Salvar valores para restaurar
      document.body.setAttribute('data-scroll-y', scrollY);
      document.body.setAttribute('data-original-overflow', originalBodyOverflow);
      document.body.setAttribute('data-original-position', originalBodyPosition);
      document.body.setAttribute('data-original-top', originalBodyTop);
      document.documentElement.setAttribute('data-original-overflow', originalHtmlOverflow);

      // Adicionar classes
      document.body.classList.add('modal-open');
      document.documentElement.classList.add('modal-open');

    } else {
      // Restaurar tudo
      const scrollY = document.body.getAttribute('data-scroll-y') || '0';
      const originalBodyOverflow = document.body.getAttribute('data-original-overflow') || '';
      const originalHtmlOverflow = document.documentElement.getAttribute('data-original-overflow') || '';
      const originalBodyPosition = document.body.getAttribute('data-original-position') || '';
      const originalBodyTop = document.body.getAttribute('data-original-top') || '';

      // Remover estilos inline forçados
      document.body.style.removeProperty('overflow');
      document.body.style.removeProperty('overflow-x');
      document.body.style.removeProperty('overflow-y');
      document.body.style.removeProperty('position');
      document.body.style.removeProperty('top');
      document.body.style.removeProperty('width');
      document.body.style.removeProperty('height');

      document.documentElement.style.removeProperty('overflow');
      document.documentElement.style.removeProperty('overflow-x');
      document.documentElement.style.removeProperty('overflow-y');

      // Restaurar valores originais
      document.body.style.overflow = originalBodyOverflow;
      document.body.style.position = originalBodyPosition;
      document.body.style.top = originalBodyTop;
      document.documentElement.style.overflow = originalHtmlOverflow;

      // Remover classes
      document.body.classList.remove('modal-open');
      document.documentElement.classList.remove('modal-open');

      // Remover CSS dinâmico
      const styleElement = document.getElementById('modal-scroll-lock');
      if (styleElement) {
        styleElement.remove();
      }

      // Remover atributos
      document.body.removeAttribute('data-scroll-y');
      document.body.removeAttribute('data-original-overflow');
      document.body.removeAttribute('data-original-position');
      document.body.removeAttribute('data-original-top');
      document.documentElement.removeAttribute('data-original-overflow');

      // Restaurar posição do scroll
      window.scrollTo(0, parseInt(scrollY));
    }

    // Cleanup quando o componente é desmontado
    return () => {
      const scrollY = document.body.getAttribute('data-scroll-y') || '0';
      const originalBodyOverflow = document.body.getAttribute('data-original-overflow') || '';
      const originalHtmlOverflow = document.documentElement.getAttribute('data-original-overflow') || '';
      const originalBodyPosition = document.body.getAttribute('data-original-position') || '';
      const originalBodyTop = document.body.getAttribute('data-original-top') || '';

      // Remover estilos inline forçados
      document.body.style.removeProperty('overflow');
      document.body.style.removeProperty('overflow-x');
      document.body.style.removeProperty('overflow-y');
      document.body.style.removeProperty('position');
      document.body.style.removeProperty('top');
      document.body.style.removeProperty('width');
      document.body.style.removeProperty('height');

      document.documentElement.style.removeProperty('overflow');
      document.documentElement.style.removeProperty('overflow-x');
      document.documentElement.style.removeProperty('overflow-y');

      // Restaurar valores originais
      document.body.style.overflow = originalBodyOverflow;
      document.body.style.position = originalBodyPosition;
      document.body.style.top = originalBodyTop;
      document.documentElement.style.overflow = originalHtmlOverflow;

      document.body.classList.remove('modal-open');
      document.documentElement.classList.remove('modal-open');

      // Remover CSS dinâmico
      const styleElement = document.getElementById('modal-scroll-lock');
      if (styleElement) {
        styleElement.remove();
      }

      document.body.removeAttribute('data-scroll-y');
      document.body.removeAttribute('data-original-overflow');
      document.body.removeAttribute('data-original-position');
      document.body.removeAttribute('data-original-top');
      document.documentElement.removeAttribute('data-original-overflow');

      if (scrollY !== '0') {
        window.scrollTo(0, parseInt(scrollY));
      }
    };
  }, [isOpen]);
};

/**
 * Hook para modais que sempre estão abertos (não dependem de isOpen)
 */
export const useModalScrollLockAlways = () => {
  useEffect(() => {
    // VERSÃO SIMPLES E SEGURA - SEM INTERVALS OU OBSERVERS
    const scrollY = window.scrollY;

    // Salvar valores originais
    const originalBodyOverflow = document.body.style.overflow;
    const originalHtmlOverflow = document.documentElement.style.overflow;
    const originalBodyPosition = document.body.style.position;
    const originalBodyTop = document.body.style.top;

    // Aplicar bloqueio de scroll simples
    document.body.style.overflow = 'hidden';
    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = '100%';

    document.documentElement.style.overflow = 'hidden';

    // Salvar valores para restaurar
    document.body.setAttribute('data-scroll-y', scrollY);
    document.body.setAttribute('data-original-overflow', originalBodyOverflow);
    document.body.setAttribute('data-original-position', originalBodyPosition);
    document.body.setAttribute('data-original-top', originalBodyTop);
    document.documentElement.setAttribute('data-original-overflow', originalHtmlOverflow);

    // Adicionar classes
    document.body.classList.add('modal-open');
    document.documentElement.classList.add('modal-open');

    // Cleanup quando o componente é desmontado
    return () => {
      const scrollY = document.body.getAttribute('data-scroll-y') || '0';
      const originalBodyOverflow = document.body.getAttribute('data-original-overflow') || '';
      const originalHtmlOverflow = document.documentElement.getAttribute('data-original-overflow') || '';
      const originalBodyPosition = document.body.getAttribute('data-original-position') || '';
      const originalBodyTop = document.body.getAttribute('data-original-top') || '';

      document.body.style.overflow = originalBodyOverflow;
      document.body.style.position = originalBodyPosition;
      document.body.style.top = originalBodyTop;
      document.body.style.width = '';

      document.documentElement.style.overflow = originalHtmlOverflow;

      document.body.classList.remove('modal-open');
      document.documentElement.classList.remove('modal-open');

      document.body.removeAttribute('data-scroll-y');
      document.body.removeAttribute('data-original-overflow');
      document.body.removeAttribute('data-original-position');
      document.body.removeAttribute('data-original-top');
      document.documentElement.removeAttribute('data-original-overflow');

      if (scrollY !== '0') {
        window.scrollTo(0, parseInt(scrollY));
      }
    };
  }, []);
};
